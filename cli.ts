#!/usr/bin/env bun
import fs from 'fs';
import { AdRedirectDetector } from './ad-redirect-detector';

/**
 * 命令行参数解析
 */
function parseArgs() {
  const args = process.argv.slice(2);
  const options: any = {
    input: null,
    type: 'html', // html, url, file
    timeout: 8000,
    headless: true,
    blockImages: true,
    blockTracking: true,
    output: null,
    verbose: false,
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    switch (arg) {
      case '-h':
      case '--help':
        showHelp();
        process.exit(0);
        break;

      case '-i':
      case '--input':
        options.input = args[++i];
        break;

      case '-t':
      case '--type':
        options.type = args[++i];
        break;

      case '--timeout':
        // @ts-ignore
        options.timeout = parseInt(args[++i]);
        break;

      case '--no-headless':
        options.headless = false;
        break;

      case '--no-block-images':
        options.blockImages = false;
        break;

      case '--no-block-tracking':
        options.blockTracking = false;
        break;

      case '-o':
      case '--output':
        options.output = args[++i];
        break;

      case '-v':
      case '--verbose':
        options.verbose = true;
        break;

      default:
        // @ts-ignore
        if (!options.input && !arg.startsWith('-')) {
          options.input = arg;
        }
        break;
    }
  }

  return options;
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
HTML 广告素材违规自动跳转检测工具 - 命令行版本

用法:
  bun run cli.ts [选项] <输入>

选项:
  -i, --input <value>     输入内容（HTML内容、URL或文件路径）
  -t, --type <type>       输入类型: html, url, file (默认: html)
  --timeout <ms>          检测超时时间，毫秒 (默认: 8000)
  --no-headless          显示浏览器窗口
  --no-block-images      不阻止图片加载
  --no-block-tracking    不阻止跟踪请求
  -o, --output <file>     输出报告到文件
  -v, --verbose          详细输出
  -h, --help             显示此帮助信息

示例:
  # 检测HTML内容
  bun run cli.ts -t html '<html><script>setTimeout(() => location.href="http://bad.com", 1000);</script></html>'
  
  # 检测HTML文件
  bun run cli.ts -t file ./test-ad.html
  
  # 检测URL
  bun run cli.ts -t url https://example.com/ad.html
  
  # 输出到文件
  bun run cli.ts -t file ./ad.html -o report.txt
  
  # 详细模式
  bun run cli.ts -t file ./ad.html -v
`);
}

/**
 * 主函数
 */
async function main() {
  const options = parseArgs();

  if (!options.input) {
    console.error('错误: 请提供输入内容');
    console.error('使用 --help 查看使用说明');
    process.exit(1);
  }

  console.log('HTML 广告素材违规跳转检测工具');
  console.log('='.repeat(50));

  if (options.verbose) {
    console.log('配置信息:');
    console.log(`  输入类型: ${options.type}`);
    console.log(`  超时时间: ${options.timeout}ms`);
    console.log(`  无头模式: ${options.headless ? '是' : '否'}`);
    console.log(`  阻止图片: ${options.blockImages ? '是' : '否'}`);
    console.log(`  阻止跟踪: ${options.blockTracking ? '是' : '否'}`);
    console.log('');
  }

  const detector = new AdRedirectDetector({
    timeout: options.timeout,
    headless: options.headless,
    blockImages: options.blockImages,
    blockTracking: options.blockTracking,
  });

  try {
    let result;
    const startTime = Date.now();

    switch (options.type) {
      case 'html':
        console.log('检测HTML内容...');
        result = await detector.detectFromHtml(options.input);
        break;

      case 'url':
        console.log(`检测URL: ${options.input}`);
        result = await detector.detectFromUrl(options.input);
        break;

      case 'file':
        console.log(`检测文件: ${options.input}`);
        if (!fs.existsSync(options.input)) {
          throw new Error(`文件不存在: ${options.input}`);
        }
        const htmlContent = fs.readFileSync(options.input, 'utf-8');
        result = await detector.detectFromHtml(htmlContent);
        break;

      default:
        throw new Error(`不支持的输入类型: ${options.type}`);
    }

    const totalTime = Date.now() - startTime;

    // 生成报告
    const report = detector.generateReport(result);

    // 输出结果
    console.log('\n检测完成!');
    console.log(`总耗时: ${totalTime}ms`);
    console.log('');
    console.log(report);

    // 简要统计
    console.log('\n检测统计:');
    console.log(`  违规状态: ${result.hasViolation ? '发现违规 ❌' : '检测正常 ✅'}`);
    console.log(`  跳转数量: ${result.redirects.length}`);
    console.log(`  触发器数: ${result.triggers.length}`);
    console.log(`  阻止请求: ${result.blockedRequests}`);
    console.log(`  检测耗时: ${result.detectionTime}ms`);

    // 输出到文件
    if (options.output) {
      const outputContent = `检测时间: ${new Date().toISOString()}\n输入: ${options.input}\n类型: ${
        options.type
      }\n\n${report}`;
      fs.writeFileSync(options.output, outputContent);
      console.log(`\n报告已保存到: ${options.output}`);
    }

    // 设置退出码
    process.exit(result.hasViolation ? 1 : 0);
  } catch (error) {
    console.error('\n检测失败:', error);
    process.exit(2);
  } finally {
    await detector.close();
  }
}

// 运行主函数
if (import.meta.main) {
  main().catch((error) => {
    console.error('程序异常:', error);
    process.exit(2);
  });
}
