# HTML 广告素材违规自动跳转检测工具 - 使用指南

## 工具概述

这是一个基于 Playwright Chromium 浏览器引擎的 HTML 广告素材违规自动跳转检测工具，能够有效识别广告素材中的未经用户交互的自动跳转行为。

## 核心功能

✅ **自动跳转检测**: 识别各种类型的自动跳转行为  
✅ **触发器分析**: 详细分析跳转触发机制  
✅ **性能优化**: 屏蔽不必要的网络请求  
✅ **详细报告**: 生成完整的检测报告  

## 检测能力

### 支持的跳转类型
- **Meta Refresh**: `<meta http-equiv="refresh" content="3;url=...">`
- **JavaScript 跳转**: `location.href`, `location.assign()`, `location.replace()`
- **定时器跳转**: `setTimeout()`, `setInterval()` 中的跳转逻辑
- **弹窗检测**: `window.open()` 调用
- **页面导航**: 监听所有导航事件

### 性能优化特性
- 🚀 自动屏蔽图片加载
- 🚀 阻止第三方跟踪脚本
- 🚀 预设跟踪域名黑名单
- 🚀 可自定义阻止域名列表

## 快速开始

### 1. 基本检测

```typescript
import { detectRedirectFromHtml } from './ad-redirect-detector';

const htmlContent = `
  <!DOCTYPE html>
  <html>
  <head>
    <meta http-equiv="refresh" content="3;url=https://malicious-site.com">
  </head>
  <body>
    <h1>广告内容</h1>
    <script>
      setTimeout(() => location.href = 'https://redirect.com', 2000);
    </script>
  </body>
  </html>
`;

const result = await detectRedirectFromHtml(htmlContent);

console.log('检测结果:', result.hasViolation ? '发现违规' : '正常');
console.log('跳转数量:', result.redirects.length);
console.log('触发器数量:', result.triggers.length);
```

### 2. 高级配置

```typescript
import { AdRedirectDetector } from './ad-redirect-detector';

const detector = new AdRedirectDetector({
  timeout: 8000,              // 检测超时时间（毫秒）
  headless: true,             // 无头模式
  blockImages: true,          // 阻止图片加载
  blockTracking: true,        // 阻止跟踪请求
  blockThirdPartyScripts: true, // 阻止第三方脚本
  blockedDomains: [           // 自定义阻止域名
    'google-analytics.com',
    'facebook.com',
    'doubleclick.net'
  ]
});

const result = await detector.detectFromHtml(htmlContent);
const report = detector.generateReport(result);
console.log(report);

await detector.close(); // 记得关闭浏览器
```

### 3. 批量检测

```typescript
const detector = new AdRedirectDetector();
const htmlFiles = ['ad1.html', 'ad2.html', 'ad3.html'];

for (const file of htmlFiles) {
  const content = fs.readFileSync(file, 'utf-8');
  const result = await detector.detectFromHtml(content);
  
  if (result.hasViolation) {
    console.log(`${file}: 发现违规跳转`);
    console.log(detector.generateReport(result));
  } else {
    console.log(`${file}: 检测正常`);
  }
}

await detector.close();
```

## 检测结果解读

### RedirectDetectionResult 结构

```typescript
{
  hasViolation: boolean,        // 是否检测到违规跳转
  redirects: RedirectInfo[],    // 跳转详情列表
  detectionTime: number,        // 检测耗时（毫秒）
  blockedRequests: number,      // 被阻止的请求数量
  triggers: TriggerInfo[]       // 检测到的潜在跳转触发器
}
```

### 跳转信息 (RedirectInfo)

```typescript
{
  targetUrl: string,           // 跳转目标URL
  timestamp: number,           // 跳转发生时间
  type: string,               // 跳转类型
  userInitiated: boolean,     // 是否为用户主动触发
  reason: string,             // 触发跳转的原因描述
  context?: string            // 相关的DOM元素或脚本信息
}
```

### 触发器信息 (TriggerInfo)

```typescript
{
  type: string,               // 触发器类型
  content: string,            // 触发器内容
  timestamp: number,          // 发现时间
  element?: string            // 相关元素
}
```

## 运行示例

### 运行所有示例
```bash
bun run example
```

### 运行测试套件
```bash
bun run test
```

### 运行快速测试
```bash
bun run quick-test.ts
```

## 检测报告示例

```
=== HTML 广告素材违规跳转检测报告 ===

检测结果: 发现违规跳转
检测耗时: 4523ms
阻止请求数: 12

检测到的跳转行为:
1. [自动跳转] navigation
   目标URL: https://malicious-site.com
   发生时间: 3021ms
   原因: 自动跳转导航
   上下文: Frame navigation to https://malicious-site.com

检测到的潜在跳转触发器:
1. [meta_refresh] 3;url=https://malicious-site.com
   相关元素: <meta http-equiv="refresh" content="3;url=https://malicious-site.com">

2. [timer] Timer redirect: () => location.href = 'https://redirect.com'
   相关元素: setTimeout(2000ms)
```

## 最佳实践

### 1. 合理设置超时时间
- 简单广告: 3-5秒
- 复杂广告: 8-10秒
- 动画广告: 10-15秒

### 2. 性能优化建议
- 启用图片阻止以提高检测速度
- 使用无头模式进行批量检测
- 复用检测器实例避免重复启动浏览器

### 3. 结果判断标准
- `hasViolation = true` 且 `userInitiated = false` 表示违规
- 关注 `triggers` 数组了解具体违规原因
- 检查 `redirects` 数组了解跳转详情

## 常见问题

### Q: 为什么有些跳转显示为 chrome-error://chromewebdata/?
A: 这通常表示页面尝试跳转到无效或被阻止的URL，这本身就是一种违规行为的表现。

### Q: 如何提高检测准确性？
A: 
1. 适当增加超时时间
2. 根据广告类型调整配置
3. 关注触发器信息而不仅仅是跳转结果

### Q: 工具是否支持检测iframe中的跳转？
A: 目前主要检测主框架的跳转，iframe跳转检测在后续版本中会加强。

## 技术支持

如果遇到问题或需要功能增强，请查看：
- README.md - 完整文档
- quick-test.ts - 快速测试
