import fs from 'fs';
import { AdRedirectDetector, detectRedirectFromHtml } from './ad-redirect-detector';

/**
 * 示例：检测HTML内容中的违规跳转
 */
async function exampleDetectHtml() {
  console.log('=== 示例1: 检测HTML内容中的违规跳转 ===\n');

  // 创建一个包含自动跳转的HTML示例
  const maliciousHtml = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>广告素材测试</title>
      <meta http-equiv="refresh" content="3;url=https://malicious-site.com">
    </head>
    <body>
      <h1>广告内容</h1>
      <img src="ad-banner.jpg" alt="广告横幅">
      
      <script>
        // 3秒后自动跳转
        setTimeout(function() {
          window.location.href = 'https://redirect-target.com';
        }, 3000);
        
        // 发送跟踪请求
        fetch('https://google-analytics.com/collect?tid=UA-12345-1');
      </script>
    </body>
    </html>
  `;

  try {
    const result = await detectRedirectFromHtml(maliciousHtml, {
      timeout: 5000,
      headless: true,
      blockTracking: true,
    });

    console.log('检测结果:');
    console.log(`- 是否存在违规: ${result.hasViolation ? '是' : '否'}`);
    console.log(`- 检测耗时: ${result.detectionTime}ms`);
    console.log(`- 阻止的请求数: ${result.blockedRequests}`);
    console.log(`- 发现的跳转: ${result.redirects.length}个`);
    console.log(`- 发现的触发器: ${result.triggers.length}个`);

    if (result.redirects.length > 0) {
      console.log('\n跳转详情:');
      result.redirects.forEach((redirect, index) => {
        console.log(`${index + 1}. ${redirect.userInitiated ? '[用户触发]' : '[自动跳转]'}`);
        console.log(`   类型: ${redirect.type}`);
        console.log(`   目标: ${redirect.targetUrl}`);
        console.log(`   时间: ${redirect.timestamp}ms`);
        console.log(`   原因: ${redirect.reason}`);
      });
    }

    if (result.triggers.length > 0) {
      console.log('\n触发器详情:');
      result.triggers.forEach((trigger, index) => {
        console.log(`${index + 1}. [${trigger.type}] ${trigger.content}`);
        if (trigger.element) {
          console.log(`   元素: ${trigger.element.substring(0, 100)}...`);
        }
      });
    }
  } catch (error) {
    console.error('检测失败:', error);
  }
}

/**
 * 示例：检测正常HTML内容（无违规跳转）
 */
async function exampleDetectNormalHtml() {
  console.log('\n=== 示例2: 检测正常HTML内容 ===\n');

  const normalHtml = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>正常广告素材</title>
    </head>
    <body>
      <h1>广告内容</h1>
      <img src="ad-banner.jpg" alt="广告横幅">
      <a href="https://advertiser-site.com" target="_blank">点击了解更多</a>
      
      <script>
        // 正常的广告展示逻辑
        console.log('广告已加载');
        
        // 用户点击时才跳转
        document.querySelector('a').addEventListener('click', function(e) {
          console.log('用户点击了广告');
        });
      </script>
    </body>
    </html>
  `;

  try {
    const result = await detectRedirectFromHtml(normalHtml, {
      timeout: 3000,
      headless: true,
    });

    console.log('检测结果:');
    console.log(`- 是否存在违规: ${result.hasViolation ? '是' : '否'}`);
    console.log(`- 检测耗时: ${result.detectionTime}ms`);
    console.log(`- 发现的跳转: ${result.redirects.length}个`);
    console.log(`- 发现的触发器: ${result.triggers.length}个`);
  } catch (error) {
    console.error('检测失败:', error);
  }
}

/**
 * 示例：使用检测器类进行批量检测
 */
async function exampleBatchDetection() {
  console.log('\n=== 示例3: 批量检测多个HTML文件 ===\n');

  const detector = new AdRedirectDetector({
    timeout: 4000,
    headless: true,
    blockImages: true,
    blockTracking: true,
    blockedDomains: ['google-analytics.com', 'facebook.com', 'doubleclick.net'],
  });

  const testCases = [
    {
      name: '包含setTimeout跳转的HTML',
      html: `
        <html><body>
          <script>setTimeout(() => location.href = 'http://bad-site.com', 2000);</script>
        </body></html>
      `,
    },
    {
      name: '包含location.assign的HTML',
      html: `
        <html><body>
          <script>
            window.onload = function() {
              location.assign('http://redirect-site.com');
            };
          </script>
        </body></html>
      `,
    },
    {
      name: '包含window.open的HTML',
      html: `
        <html><body>
          <script>
            setTimeout(() => window.open('http://popup-site.com'), 1000);
          </script>
        </body></html>
      `,
    },
    {
      name: '正常的HTML',
      html: `
        <html><body>
          <h1>正常内容</h1>
          <a href="http://normal-site.com">正常链接</a>
        </body></html>
      `,
    },
  ];

  try {
    for (const testCase of testCases) {
      console.log(`检测: ${testCase.name}`);
      const result = await detector.detectFromHtml(testCase.html);

      console.log(`  结果: ${result.hasViolation ? '违规' : '正常'}`);
      console.log(`  跳转数: ${result.redirects.length}`);
      console.log(`  触发器数: ${result.triggers.length}`);
      console.log(`  耗时: ${result.detectionTime}ms`);

      if (result.hasViolation) {
        console.log('  详细报告:');
        const report = detector.generateReport(result);
        console.log(
          report
            .split('\n')
            .map((line) => '    ' + line)
            .join('\n')
        );
      }
      console.log('');
    }
  } catch (error) {
    console.error('批量检测失败:', error);
  } finally {
    await detector.close();
  }
}

/**
 * 示例：从文件检测
 */
async function exampleDetectFromFile() {
  console.log('\n=== 示例4: 从文件检测 ===\n');

  // 创建测试HTML文件
  const testHtmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>测试广告</title>
    </head>
    <body>
      <div id="ad-container">
        <h2>广告内容</h2>
        <img src="https://example.com/ad.jpg" alt="广告图片">
      </div>
      
      <script>
        // 模拟恶意跳转
        let clickCount = 0;
        document.addEventListener('click', function() {
          clickCount++;
          if (clickCount > 0) {
            // 即使用户点击了，也进行额外的自动跳转
            setTimeout(() => {
              window.location.replace('https://malicious-redirect.com');
            }, 500);
          }
        });
        
        // 页面加载后自动跳转
        window.addEventListener('load', function() {
          setTimeout(() => {
            location.href = 'https://auto-redirect.com';
          }, 4000);
        });
      </script>
    </body>
    </html>
  `;

  const testFilePath = 'test-ad.html';

  try {
    // 写入测试文件
    fs.writeFileSync(testFilePath, testHtmlContent);
    console.log(`已创建测试文件: ${testFilePath}`);

    // 读取并检测
    const htmlContent = fs.readFileSync(testFilePath, 'utf-8');
    const result = await detectRedirectFromHtml(htmlContent, {
      timeout: 6000,
      headless: true,
    });

    console.log('\n检测结果:');
    const detector = new AdRedirectDetector();
    const report = detector.generateReport(result);
    console.log(report);

    // 清理测试文件
    fs.unlinkSync(testFilePath);
    console.log('已清理测试文件');
  } catch (error) {
    console.error('文件检测失败:', error);
  }
}

/**
 * 主函数：运行所有示例
 */
async function main() {
  console.log('HTML 广告素材违规自动跳转检测工具 - 使用示例\n');

  try {
    await exampleDetectHtml();
    await exampleDetectNormalHtml();
    await exampleBatchDetection();
    await exampleDetectFromFile();

    console.log('\n=== 所有示例执行完成 ===');
  } catch (error) {
    console.error('示例执行失败:', error);
  }
}

// 如果直接运行此文件，则执行示例
if (import.meta.main) {
  main().catch(console.error);
}

export { main as runExamples };
