// HTML 广告素材违规自动跳转检测批量处理脚本
import { AdRedirectDetector } from './ad-redirect-detector';
import fs from 'fs';
import path from 'path';

/**
 * 广告数据接口
 */
interface AdData {
  ad_id: string;
  adm: string;
}

/**
 * 检测结果接口
 */
interface BatchDetectionResult {
  ad_id: string;
  hasViolation: boolean;
  redirects: number;
  triggers: number;
  detectionTime: number;
  errorMessage?: string;
}

/**
 * 统计信息接口
 */
interface DetectionStats {
  totalFiles: number;
  totalAds: number;
  processedAds: number;
  violationAds: number;
  errorAds: number;
  violationRate: number;
  totalProcessingTime: number;
  averageProcessingTime: number;
}

/**
 * 读取 source 目录下的所有 JSON 文件
 */
function loadJsonFiles(): { fileName: string; data: AdData[] }[] {
  const sourceDir = path.join(__dirname, 'source');
  const files: { fileName: string; data: AdData[] }[] = [];

  if (!fs.existsSync(sourceDir)) {
    console.error('❌ source 目录不存在');
    return files;
  }

  const fileNames = fs.readdirSync(sourceDir).filter(file => file.endsWith('.json'));

  for (const fileName of fileNames) {
    try {
      const filePath = path.join(sourceDir, fileName);
      const fileContent = fs.readFileSync(filePath, 'utf-8');
      const data: AdData[] = JSON.parse(fileContent);

      if (Array.isArray(data)) {
        files.push({ fileName, data });
        console.log(`✅ 成功加载文件: ${fileName} (${data.length} 个广告)`);
      } else {
        console.warn(`⚠️  文件格式不正确: ${fileName} (不是数组格式)`);
      }
    } catch (error) {
      console.error(`❌ 加载文件失败: ${fileName}`, error);
    }
  }

  return files;
}

/**
 * 检测单个广告素材
 */
async function detectSingleAd(
  detector: AdRedirectDetector,
  adData: AdData,
  index: number,
  total: number
): Promise<BatchDetectionResult> {
  const startTime = Date.now();

  try {
    console.log(`🔍 检测广告 ${index + 1}/${total}: ${adData.ad_id}`);

    // 检测广告素材
    const result = await detector.detectFromHtml(adData.adm);

    const detectionTime = Date.now() - startTime;

    // 输出简要结果
    const status = result.hasViolation ? '❌ 违规' : '✅ 正常';
    console.log(`   ${status} | 跳转:${result.redirects.length} | 触发器:${result.triggers.length} | 耗时:${detectionTime}ms`);

    return {
      ad_id: adData.ad_id,
      hasViolation: result.hasViolation,
      redirects: result.redirects.length,
      triggers: result.triggers.length,
      detectionTime,
    };

  } catch (error) {
    const detectionTime = Date.now() - startTime;
    console.error(`   ❌ 检测失败: ${error}`);

    return {
      ad_id: adData.ad_id,
      hasViolation: false,
      redirects: 0,
      triggers: 0,
      detectionTime,
      errorMessage: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * 批量检测广告素材
 */
async function batchDetectAds(files: { fileName: string; data: AdData[] }[]): Promise<{
  results: BatchDetectionResult[];
  stats: DetectionStats;
}> {
  const detector = new AdRedirectDetector({
    timeout: 6000,
    headless: true,
    blockImages: true,
    blockTracking: true,
    blockThirdPartyScripts: true,
  });

  const results: BatchDetectionResult[] = [];
  const startTime = Date.now();

  let totalAds = 0;
  let processedAds = 0;
  let violationAds = 0;
  let errorAds = 0;

  // 计算总广告数
  for (const file of files) {
    totalAds += file.data.length;
  }

  console.log(`\n🚀 开始批量检测 ${files.length} 个文件，共 ${totalAds} 个广告\n`);

  try {
    // 逐文件处理
    for (const file of files) {
      console.log(`\n📁 处理文件: ${file.fileName} (${file.data.length} 个广告)`);
      console.log('=' .repeat(60));

      // 逐个检测广告
      for (let i = 0; i < file.data.length; i++) {
        const adData = file.data[i]!;
        const result = await detectSingleAd(detector, adData, processedAds, totalAds);

        results.push(result);
        processedAds++;

        if (result.hasViolation) {
          violationAds++;
        }

        if (result.errorMessage) {
          errorAds++;
        }

        // 每处理 10 个广告显示一次进度
        if (processedAds % 10 === 0) {
          const progress = ((processedAds / totalAds) * 100).toFixed(1);
          console.log(`📊 进度: ${processedAds}/${totalAds} (${progress}%) | 违规: ${violationAds} | 错误: ${errorAds}`);
        }
      }
    }

    const totalProcessingTime = Date.now() - startTime;
    const averageProcessingTime = totalProcessingTime / processedAds;
    const violationRate = (violationAds / processedAds) * 100;

    const stats: DetectionStats = {
      totalFiles: files.length,
      totalAds,
      processedAds,
      violationAds,
      errorAds,
      violationRate,
      totalProcessingTime,
      averageProcessingTime,
    };

    return { results, stats };

  } finally {
    await detector.close();
  }
}

/**
 * 生成详细检测报告
 */
function generateDetailedReport(
  results: BatchDetectionResult[],
  stats: DetectionStats
): string {
  const report = [];

  report.push('='.repeat(80));
  report.push('HTML 广告素材违规自动跳转检测 - 批量处理报告');
  report.push('='.repeat(80));
  report.push('');

  // 统计信息
  report.push('📊 检测统计:');
  report.push(`   处理文件数: ${stats.totalFiles}`);
  report.push(`   总广告数量: ${stats.totalAds}`);
  report.push(`   成功检测: ${stats.processedAds}`);
  report.push(`   检测到违规: ${stats.violationAds}`);
  report.push(`   检测失败: ${stats.errorAds}`);
  report.push(`   违规率: ${stats.violationRate.toFixed(2)}%`);
  report.push(`   总处理时间: ${(stats.totalProcessingTime / 1000).toFixed(2)}秒`);
  report.push(`   平均处理时间: ${stats.averageProcessingTime.toFixed(0)}ms/个`);
  report.push('');

  // 违规广告列表
  const violationResults = results.filter(r => r.hasViolation);
  if (violationResults.length > 0) {
    report.push('❌ 检测到违规的广告ID列表:');
    report.push('-'.repeat(60));
    violationResults.forEach((result, index) => {
      report.push(`${index + 1}. ${result.ad_id}`);
      report.push(`   跳转数: ${result.redirects} | 触发器数: ${result.triggers} | 耗时: ${result.detectionTime}ms`);
    });
    report.push('');
  }

  // 错误广告列表
  const errorResults = results.filter(r => r.errorMessage);
  if (errorResults.length > 0) {
    report.push('⚠️  检测失败的广告ID列表:');
    report.push('-'.repeat(60));
    errorResults.forEach((result, index) => {
      report.push(`${index + 1}. ${result.ad_id}`);
      report.push(`   错误信息: ${result.errorMessage}`);
    });
    report.push('');
  }

  // 正常广告统计
  const normalResults = results.filter(r => !r.hasViolation && !r.errorMessage);
  report.push(`✅ 正常广告数量: ${normalResults.length}`);
  report.push('');

  report.push('='.repeat(80));
  report.push(`报告生成时间: ${new Date().toLocaleString()}`);
  report.push('='.repeat(80));

  return report.join('\n');
}

/**
 * 保存结果到文件
 */
function saveResults(
  results: BatchDetectionResult[],
  stats: DetectionStats
): void {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

  // 保存详细报告
  const report = generateDetailedReport(results, stats);
  const reportPath = `dist/detection-report-${timestamp}.txt`;
  fs.writeFileSync(reportPath, report);
  console.log(`📄 详细报告已保存: ${reportPath}`);

  // 保存 JSON 结果
  const jsonData = {
    timestamp: new Date().toISOString(),
    stats,
    results,
  };
  const jsonPath = `dist/detection-results-${timestamp}.json`;
  fs.writeFileSync(jsonPath, JSON.stringify(jsonData, null, 2));
  console.log(`📊 JSON 结果已保存: ${jsonPath}`);

  // 保存违规广告ID列表
  const violationIds = results.filter(r => r.hasViolation).map(r => r.ad_id);
  if (violationIds.length > 0) {
    const violationPath = `dist/violation-ads-${timestamp}.txt`;
    fs.writeFileSync(violationPath, violationIds.join('\n'));
    console.log(`🚨 违规广告ID列表已保存: ${violationPath}`);
  }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  console.log('🎯 HTML 广告素材违规自动跳转检测工具 - 批量处理模式');
  console.log('='.repeat(80));

  try {
    // 确保输出目录存在
    const distDir = path.join(__dirname, 'dist');
    if (!fs.existsSync(distDir)) {
      fs.mkdirSync(distDir, { recursive: true });
    }

    // 加载 JSON 文件
    console.log('📂 正在加载 source 目录下的 JSON 文件...');
    const files = loadJsonFiles();

    if (files.length === 0) {
      console.error('❌ 未找到有效的 JSON 文件，程序退出');
      return;
    }

    // 批量检测
    const { results, stats } = await batchDetectAds(files);

    // 显示最终统计
    console.log('\n' + '='.repeat(80));
    console.log('🎉 批量检测完成！');
    console.log('='.repeat(80));
    console.log(`📊 处理统计:`);
    console.log(`   文件数: ${stats.totalFiles}`);
    console.log(`   广告总数: ${stats.totalAds}`);
    console.log(`   成功检测: ${stats.processedAds}`);
    console.log(`   违规广告: ${stats.violationAds} (${stats.violationRate.toFixed(2)}%)`);
    console.log(`   检测失败: ${stats.errorAds}`);
    console.log(`   总耗时: ${(stats.totalProcessingTime / 1000).toFixed(2)}秒`);
    console.log(`   平均耗时: ${stats.averageProcessingTime.toFixed(0)}ms/个`);

    // 保存结果
    console.log('\n💾 正在保存检测结果...');
    saveResults(results, stats);

    console.log('\n✨ 所有任务完成！');

    // 如果有违规广告，以非零退出码退出
    if (stats.violationAds > 0) {
      console.log(`\n🚨 检测到 ${stats.violationAds} 个违规广告，请查看详细报告`);
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ 程序执行失败:', error);
    process.exit(2);
  }
}

// 运行主函数
if (import.meta.main) {
  main().catch((error) => {
    console.error('💥 程序异常退出:', error);
    process.exit(2);
  });
}
