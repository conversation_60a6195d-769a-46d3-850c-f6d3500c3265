{"name": "demo-playwright", "module": "index.ts", "type": "module", "private": true, "scripts": {"start": "bun run index.ts", "example": "bun run example-usage.ts", "test": "bun run test-detector.ts", "quick-test": "bun run quick-test.ts", "detect": "bun run cli.ts", "cli": "bun run cli.ts"}, "devDependencies": {"@types/bun": "latest", "@types/node": "^20.0.0"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"playwright": "^1.55.0"}}